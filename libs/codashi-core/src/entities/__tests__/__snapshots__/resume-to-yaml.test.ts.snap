// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`resumeToYaml > should successfully convert a comprehensive Resume with all possible fields to YAML 1`] = `
"basics:
  name: <PERSON>
  title: Senior Software Engineer
  image: https://example.com/jane-smith.jpg
  email: <EMAIL>
  phone: (*************
  url: https://janesmith.dev
  summary: >-
    Experienced software engineer with a focus on **cloud architecture** and
    {{technology}} systems.

    Over 10 years of industry experience building scalable applications.
  location:
    address: 123 Tech Street
    postal_code: "10001"
    city: New York
    country_code: US
    region: NY
  profiles:
    - network: LinkedIn
      username: janesmith
      url: https://linkedin.com/in/janesmith
    - network: GitHub
      username: janesmith
      url: https://github.com/janesmith
    - network: Twitter
      username: janesmith
      url: https://twitter.com/janesmith
work:
  - name: Tech Solutions Inc.
    location: New York, NY
    description: A leading provider of enterprise software solutions.
    position: Lead Software Architect
    url: https://techsolutions.example.com
    start_date: 2018-01-01
    end_date: 2023-01-01
    summary: Led the architecture and development of the company's flagship product.
    highlights:
      - Increased system performance by 40%
      - Reduced deployment time from days to minutes
      - Implemented CI/CD pipeline using GitHub Actions
    positions:
      - position: Senior Developer
        start_date: 2018-01-01
        end_date: 2020-06-30
        summary: Responsible for backend development.
        highlights:
          - Developed core API
          - Improved test coverage
        transition_type: initial
      - position: Lead Software Architect
        start_date: 2020-07-01
        end_date: 2023-01-01
        summary: Led architecture decisions.
        highlights:
          - Designed microservice architecture
          - Mentored junior developers
        transition_type: promotion
        parallel_roles:
          - position: Scrum Master
            summary: Facilitated agile processes
            time_allocation: 20
volunteer:
  - organization: Code for Good
    position: Mentor
    url: https://codeforgood.org
    start_date: 2019-01-01
    end_date: 2022-12-31
    summary: Mentored underprivileged youth in software development
    highlights:
      - Taught web development to 50+ students
      - Organized annual hackathons
education:
  - institution: University of Technology
    url: https://uotech.edu
    area: Computer Science
    study_type: Bachelor
    start_date: 2011-09-01
    end_date: 2015-05-31
    score: 3.8 GPA
    courses:
      - Data Structures and Algorithms
      - Operating Systems
      - Database Management
awards:
  - title: Innovation Award
    date: 2022-06-15
    awarder: Tech Industry Association
    summary: Recognized for innovative approach to cloud architecture
certificates:
  - name: AWS Solutions Architect
    date: 2021-05-20
    url: https://aws.amazon.com/certification
    issuer: Amazon Web Services
publications:
  - name: Modern Microservice Architecture
    publisher: Tech Publishing Co.
    release_date: 2021-11-15
    url: https://example.com/publication
    summary: A comprehensive guide to designing microservice architectures
skills:
  - name: Programming Languages
    level: Expert
    keywords:
      - JavaScript
      - TypeScript
      - Python
      - Go
  - name: Cloud Technologies
    level: Expert
    keywords:
      - AWS
      - Google Cloud
      - Azure
      - Kubernetes
languages:
  - language: English
    fluency: Native
  - language: Spanish
    fluency: Professional
interests:
  - name: Open Source
    keywords:
      - Contributing
      - Maintaining
      - Community Building
references:
  - name: Alex Johnson
    reference: Jane is an exceptional engineer and leader.
    email: <EMAIL>
    phone: "5551234567"
    position: CTO
    company: Tech Solutions Inc.
projects:
  - name: Cloud Migration Framework
    description: An open-source framework for migrating legacy applications to the cloud.
    highlights:
      - 1000+ GitHub stars
      - Used by 50+ companies
    keywords:
      - Cloud
      - Migration
      - Open Source
    start_date: 2020-01-01
    end_date: 2022-06-30
    url: https://github.com/janesmith/cloud-migration
    roles:
      - Creator
      - Maintainer
    entity: Personal Project
    type: Open Source
meta:
  canonical: https://janesmith.dev/resume.json
  version: 1.0.0
  last_modified: 2023-05-01
"
`;
