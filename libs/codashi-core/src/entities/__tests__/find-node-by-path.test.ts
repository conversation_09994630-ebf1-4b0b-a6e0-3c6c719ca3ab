import { describe, expect, it } from 'vitest';
import { parseDocument } from 'yaml';

import { findNodeByPath } from '../resume.yaml';

describe('findNodeByPath', () => {
  it('should return null for empty document', () => {
    // Arrange
    const doc = parseDocument('');

    // Act
    const result = findNodeByPath(doc, ['any', 'path']);

    // Assert
    expect(result).toBeNull();
  });

  it('should find a top-level property in a YAML map', () => {
    // Arrange
    const yamlString = `
name: <PERSON>
age: 30
`;
    const doc = parseDocument(yamlString);

    // Act
    const nameNode = findNodeByPath(doc, ['name']);
    const ageNode = findNodeByPath(doc, ['age']);

    // Assert
    expect(nameNode?.toString()).toBe('<PERSON>');
    expect(ageNode?.toString()).toBe('30');
  });

  it('should find a nested property in a YAML map', () => {
    // Arrange
    const yamlString = `
person:
  name: <PERSON>
  contact:
    email: <EMAIL>
    phone: ************
`;
    const doc = parseDocument(yamlString);

    // Act
    const nameNode = findNodeByPath(doc, ['person', 'name']);
    const emailNode = findNodeByPath(doc, ['person', 'contact', 'email']);

    // Assert
    expect(nameNode?.toString()).toBe('John Doe');
    expect(emailNode?.toString()).toBe('<EMAIL>');
  });

  it('should find items in a YAML sequence by index', () => {
    // Arrange
    const yamlString = `
fruits:
  - apple
  - banana
  - cherry
`;
    const doc = parseDocument(yamlString);

    // Act
    const firstFruit = findNodeByPath(doc, ['fruits', '0']);
    const secondFruit = findNodeByPath(doc, ['fruits', '1']);

    // Assert
    expect(firstFruit?.toString()).toBe('apple');
    expect(secondFruit?.toString()).toBe('banana');
  });

  it('should find items in a nested YAML sequence', () => {
    // Arrange
    const yamlString = `
shopping:
  groceries:
    - name: apple
      quantity: 5
    - name: banana
      quantity: 3
`;
    const doc = parseDocument(yamlString);

    // Act
    const firstItemName = findNodeByPath(doc, [
      'shopping',
      'groceries',
      '0',
      'name',
    ]);
    const secondItemQuantity = findNodeByPath(doc, [
      'shopping',
      'groceries',
      '1',
      'quantity',
    ]);

    // Assert
    expect(firstItemName?.toString()).toBe('apple');
    expect(secondItemQuantity?.toString()).toBe('3');
  });

  it('should return null for invalid paths', () => {
    // Arrange
    const yamlString = `
person:
  name: John Doe
  age: 30
`;
    const doc = parseDocument(yamlString);

    // Act & Assert
    // Non-existent top-level property
    expect(findNodeByPath(doc, ['address'])).toBeNull();

    // Non-existent nested property
    expect(findNodeByPath(doc, ['person', 'email'])).toBeNull();

    // Invalid path through a scalar value
    expect(findNodeByPath(doc, ['person', 'name', 'first'])).toBeNull();

    // Invalid array index
    expect(findNodeByPath(doc, ['person', '0'])).toBeNull();
  });

  it('should handle complex mixed structures', () => {
    // Arrange
    const yamlString = `
resume:
  basics:
    name: John Doe
    profiles:
      - network: GitHub
        username: johndoe
      - network: LinkedIn
        username: john-doe
  skills:
    - name: Programming
      keywords:
        - JavaScript
        - TypeScript
    - name: DevOps
      keywords:
        - Docker
        - Kubernetes
`;
    const doc = parseDocument(yamlString);

    // Act
    const name = findNodeByPath(doc, ['resume', 'basics', 'name']);
    const githubUsername = findNodeByPath(doc, [
      'resume',
      'basics',
      'profiles',
      '0',
      'username',
    ]);
    const firstSkillName = findNodeByPath(doc, [
      'resume',
      'skills',
      '0',
      'name',
    ]);
    const typescript = findNodeByPath(doc, [
      'resume',
      'skills',
      '0',
      'keywords',
      '1',
    ]);
    const docker = findNodeByPath(doc, [
      'resume',
      'skills',
      '1',
      'keywords',
      '0',
    ]);

    // Assert
    expect(name?.toString()).toBe('John Doe');
    expect(githubUsername?.toString()).toBe('johndoe');
    expect(firstSkillName?.toString()).toBe('Programming');
    expect(typescript?.toString()).toBe('TypeScript');
    expect(docker?.toString()).toBe('Docker');
  });

  it('should return null for out-of-bounds array indices', () => {
    // Arrange
    const yamlString = `
items:
  - first
  - second
  - third
`;
    const doc = parseDocument(yamlString);

    // Act & Assert
    expect(findNodeByPath(doc, ['items', '3'])).toBeNull(); // Out of bounds (only 0-2 exist)
    expect(findNodeByPath(doc, ['items', '-1'])).toBeNull(); // Negative index
    expect(findNodeByPath(doc, ['items', 'not-a-number'])).toBeNull(); // Not a number
  });
});
