import { zodToDomainOrThrow } from '@awe/core';
import z from 'zod';

const evaluation = z.object({
  ai_confidence: z
    .number()
    .min(0)
    .max(1)
    .describe("Confidence score (0-1) of the extracted data's accuracy"),
  human_feedback: z
    .discriminatedUnion('type', [
      z.object({
        type: z.literal('confirmation'),
        value: z.enum(['yes', 'no']),
      }),
      z.object({
        type: z.literal('edit'),
        value: z.string(),
      }),
    ])
    .describe('Human feedback on the extracted data')
    .nullable(),
});

export const jobDraft = z.object({
  title: z
    .array(
      z
        .object({
          value: z
            .string()
            .describe('The title of the job, e.g. Web Developer'),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of possible job titles with confidence scores'),

  company: z
    .array(
      z
        .object({
          value: z.string().describe('The name of the company, e.g. Microsoft'),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of possible company names with confidence scores'),

  type: z
    .array(
      z
        .object({
          value: z
            .string()
            .describe(
              'The type of the job, e.g. Full-time, part-time, contract, etc.'
            ),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of possible job types with confidence scores'),

  date: z
    .array(
      z
        .object({
          value: z.coerce.date().nullable().describe('When the job was posted'),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of possible job posting dates with confidence scores'),

  description: z
    .array(
      z
        .object({
          value: z.string().describe('A description of the job'),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of possible job descriptions with confidence scores'),

  location: z
    .array(
      z
        .object({
          value: z.object({
            address: z
              .string()
              .nullable()
              .describe(
                'To add multiple address lines, use \n. For example, 1234 Glücklichkeit Straße\nHinterhaus 5. Etage li.'
              ),
            postalCode: z.string().nullable(),
            city: z.string().nullable(),
            countryCode: z
              .string()
              .nullable()
              .describe('code as per ISO-3166-1 ALPHA-2, e.g. US, AU, IN'),
            region: z
              .string()
              .nullable()
              .describe(
                'The general region where you live. Can be a US state, or a province, for instance.'
              ),
          }),
        })
        .merge(evaluation)
    )
    .default([])
    .describe(
      'Array of possible job locations with confidence scores. Can be null if remote'
    ),

  remote: z
    .array(
      z
        .object({
          value: z
            .enum(['Full', 'Hybrid', 'None'])
            .nullable()
            .describe('the level of remote work available'),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of possible remote work levels with confidence scores'),

  salary: z
    .array(
      z
        .object({
          value: z
            .string()
            .nullable()
            .describe(
              'A number, for example - 100000$, or a range like $100000-200000'
            ),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of possible salary ranges with confidence scores'),

  experience: z
    .array(
      z
        .object({
          value: z
            .string()
            .nullable()
            .describe(
              'The experience level required for the job, e.g. Senior or Junior or Middle'
            ),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of possible experience levels with confidence scores'),

  responsibilities: z
    .array(
      z
        .object({
          value: z
            .array(
              z
                .string()
                .describe(
                  'The responsibilities of the job, e.g. Build out a new API for our customer base.'
                )
            )
            .nullable(),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of possible job responsibilities with confidence scores'),

  qualifications: z
    .array(
      z
        .object({
          value: z
            .array(
              z
                .string()
                .describe(
                  'Qualifications required for the job, e.g. undergraduate degree, etc.'
                )
            )
            .nullable(),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of possible job qualifications with confidence scores'),

  skills: z
    .array(
      z
        .object({
          value: z
            .array(
              z.object({
                name: z.string().describe('e.g. Web Development'),
                level: z.string().nullable().describe('e.g. Master'),
                keywords: z
                  .array(z.string().describe('e.g. HTML'))
                  .nullable()
                  .describe('List some keywords pertaining to this skill'),
              })
            )
            .nullable(),
        })
        .merge(evaluation)
    )
    .default([])
    .describe(
      'Array of possible skills required for the job with confidence scores'
    ),

  meta: z
    .array(
      z
        .object({
          value: z.object({
            canonical: z
              .string()
              .url()
              .nullable()
              .describe(
                'URL (as per RFC 3986) to latest version of this document'
              ),
            version: z
              .string()
              .nullable()
              .describe('A version field which follows semver - e.g. v1.0.0'),
            lastModified: z
              .string()
              .nullable()
              .describe('Using ISO 8601 with YYYY-MM-DDThh:mm:ss'),
          }),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of possible metadata with confidence scores'),

  // Extended fields for AI inference
  companyMeta: z
    .array(
      z
        .object({
          value: z.object({
            type: z
              .string()
              .nullable()
              .describe(
                'The type of the company, e.g. Startup, Mid-size, Large'
              ),
            size: z
              .string()
              .nullable()
              .describe(
                'The size of the company, e.g. 1-10, 11-100, 101-1000, etc.'
              ),
            tone: z
              .string()
              .nullable()
              .describe('The tone of the company, e.g. Formal, Informal, etc.'),
            internalCompanyId: z
              .string()
              .nullable()
              .describe(
                'Codashi internal company id. We use this to link to the company profile with useful information for the candidate'
              ),
          }),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of possible company metadata with confidence scores'),

  notes: z
    .array(
      z
        .object({
          value: z
            .array(z.string())
            .nullable()
            .describe(
              'Notes about the job, made by the candidate for the sake of the AI adjusting the resume'
            ),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of possible job notes with confidence scores'),
});

export type JobDraft = z.infer<typeof jobDraft>;

export const toDraft = (input: unknown) => zodToDomainOrThrow(jobDraft, input);

/*
  Job Extraction Implementation Strategy
  =====================================

  1. Hybrid Extraction Approach
     -------------------------
     - Start with 1-shot extraction for speed
     - Fall back to step-by-step for missing/low-confidence fields
     - Validate and repair the extracted data

  2. Implementation Steps
     -------------------
     a. One-shot Extraction (extractOneShot):
        - Single LLM call to extract all fields
        - Include schema and examples in the prompt
        - Request confidence scoring for each field

     b. Validation & Confidence Check:
        - Validate against Zod schema
        - Check confidence thresholds:
          * High (>0.9): Accept
          * Medium (0.7-0.9): Flag for review
          * Low (<0.7): Trigger step-by-step

     c. Step-by-step Fallback (extractStepByStep):
        - Extract one section at a time
        - Focus on critical fields first (title, company, description)
        - Merge results with 1-shot attempt

  3. Critical Fields (require high confidence):
     - title
     - company
     - description
     - type
     - location/remote

  4. Error Handling:
     - Log all validation errors
     - Provide fallback values where possible
     - Track extraction metrics for continuous improvement

  5. Optimization Opportunities:
     - Cache common patterns
     - Parallelize independent sections
     - Implement retry logic for transient failures

  Example Implementation:
  ---------------------
  async function extractJobWithFallback(jobText: string) {
      // 1. Try one-shot extraction
      const oneShotResult = await extractOneShot(jobText);

      // 2. Validate and check confidence
      const validation = jobDraft.safeParse(oneShotResult);
      if (validation.success && hasHighConfidence(oneShotResult)) {
          return oneShotResult;
      }

      // 3. Fall back to step-by-step for missing/low-confidence fields
      return extractStepByStep(jobText, validation.error);
  }

  function hasHighConfidence(result: any) {
      const criticalFields = ['title', 'company', 'description'];
      return criticalFields.every(field =>
          result[field]?.length > 0 &&
          result[field].every((item: any) => item.ai_confidence >= 0.8)
      );
  }
  */

/*
Recommendations for Job Schema Improvements:
==========================================

1. Enhanced Location Handling:
   - Add timezone field for remote work coordination
   - Include cost_of_living_index for salary comparison
   - Add office_locations array for hybrid positions
   - Support for multiple office locations
   - Example:
     ```typescript
     location: z.object({
       address: z.string().nullable(),
       city: z.string().nullable(),
       timezone: z.string().nullable(),
       cost_of_living_index: z.number().nullable(),
       office_locations: z.array(locationSchema).nullable()
     })
     ```

2. Salary & Benefits Enhancements:
   - Add currency field (USD, EUR, etc.)
   - Include benefits array (health, dental, 401k, etc.)
   - Add equity/stock options information
   - Include bonus structure details
   - Add vacation/PTO information
   - Support for salary ranges vs fixed amounts

3. Company Information:
   - Add industry field
   - Include company_stage (startup, growth, enterprise)
   - Add funding_status for startups
   - Include company_culture keywords
   - Add diversity_metrics if available
   - Include company_size_range

4. Job Requirements:
   - Separate must_have vs nice_to_have skills
   - Add years_of_experience for each skill
   - Include education_requirements
   - Add certification_requirements
   - Include language_requirements
   - Add security_clearance_required

5. Application Process:
   - Add application_deadline
   - Include interview_process_stages
   - Add expected_start_date
   - Include application_instructions
   - Add contact_information
   - Include application_url

6. Work Environment:
   - Add team_size information
   - Include reporting_structure
   - Add work_schedule (flexible, fixed, etc.)
   - Include travel_requirements
   - Add equipment_provided
   - Include development_opportunities

7. Performance & Growth:
   - Add career_progression_path
   - Include performance_review_cycle
   - Add learning_budget
   - Include conference_attendance
   - Add mentorship_opportunities
   - Include promotion_timeline

8. Technical Details:
   - Add tech_stack array with versions
   - Include development_methodology (Agile, Scrum, etc.)
   - Add code_review_process
   - Include testing_requirements
   - Add deployment_frequency
   - Include architecture_patterns

9. Compliance & Legal:
   - Add visa_sponsorship_available
   - Include background_check_required
   - Add drug_testing_policy
   - Include non_compete_agreement
   - Add confidentiality_requirements
   - Include equal_opportunity_statement

10. AI Enhancement Fields:
    - Add job_posting_quality_score
    - Include keyword_density_analysis
    - Add market_competitiveness_rating
    - Include similar_jobs_comparison
    - Add application_success_prediction
    - Include skill_gap_analysis

These enhancements would make the job schema more comprehensive for modern job matching
and candidate evaluation while maintaining flexibility for different industries and roles.
*/
