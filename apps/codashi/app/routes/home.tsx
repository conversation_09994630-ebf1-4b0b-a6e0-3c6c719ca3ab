import { useState } from 'react';
import { Form, useActionData, useSubmit } from 'react-router';
import type { Route } from './+types/home';

import { extractTextWithFallback, type Resume } from '@awe/codashi-core';
import { ResumePdfPreview } from '@awe/codashi-ui';
import { ProfileExtractionFlow } from '@self/components/profile.flow';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function meta(_: Route.MetaArgs) {
  return [
    { title: 'Resume Parser' },
    { name: 'description', content: 'Upload and parse your resume' },
  ];
}

export async function clientAction({ request }: Route.ActionArgs) {
  const formData = await request.formData();
  const file = formData.get('resume');

  if (!(file instanceof File) || file.size === 0) {
    return { error: 'Please upload a PDF file' };
  }

  try {
    const arrayBuffer = await file.arrayBuffer();
    // Create a File object with the arrayBuffer if needed by extractTextWithFallback
    const { content: text } = await extractTextWithFallback(
      new File([arrayBuffer], file.name, { type: file.type })
    );
    return { text };
  } catch (error) {
    return { error: 'Failed to process PDF. Please try another file.' };
  }
}

export function loader({ context }: Route.LoaderArgs) {
  return { message: context.cloudflare.env.AI };
}

export default function Home() {
  const submit = useSubmit();
  const actionData = useActionData<typeof clientAction>();
  const [profile, setProfile] = useState<Resume | null>(null);
  const [fileName, setFileName] = useState('');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      setFileName(file.name);

      const formData = new FormData();
      formData.append('resume', file);

      submit(formData, {
        method: 'POST',
        encType: 'multipart/form-data',
      });
    }
  };

  return (
    <main className="grid-container p-4 full-height">
      <section className="grid-span-7 flex flex-col gap-8">
        <h1 className="text-2xl font-bold">Resume Parser</h1>
        <Form>
          <div className="flex flex-col gap-2">
            <label htmlFor="resume" className="text-lg font-medium">
              Upload your resume (PDF)
            </label>
            <input
              id="resume"
              name="resume"
              type="file"
              accept="application/pdf"
              onChange={handleFileChange}
              className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              required
            />
          </div>
        </Form>

        {actionData?.text && (
          <ProfileExtractionFlow
            resumeText={actionData.text}
            onProfileReady={setProfile}
          />
        )}
      </section>

      <section className="grid-span-5">
        {actionData?.error && (
          <div className="mt-4 p-4 bg-red-50 text-red-700 rounded-md">
            {actionData.error}
          </div>
        )}

        {profile && <ResumePdfPreview profile={profile} />}
      </section>
    </main>
  );
}
